import {
  Body,
  Controller,
  Del,
  Get,
  Inject,
  Param,
  Post,
  Put,
  Query,
} from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { CustomerMembershipCardService } from '../service/customer-membership-card.service';
import { CustomError } from '../error/custom.error';
import { MembershipCardType, User } from '../entity';

@Controller('/customer-membership-cards')
export class CustomerMembershipCardController {
  @Inject()
  ctx: Context;

  @Inject()
  service: CustomerMembershipCardService;

  @Get('/', { summary: '查询用户会员卡列表' })
  async index(@Query() query: any) {
    let { offset, limit } = query;
    const { offset: o, limit: l, current, pageSize, ...queryInfo } = query;
    void o;
    void l;

    if (current && pageSize) {
      offset = (current - 1) * pageSize;
      limit = pageSize;
    }
    return this.service.findAll({
      query: queryInfo,
      offset,
      limit,
      order: [['createdAt', 'DESC']],
      include: [
        {
          model: MembershipCardType,
        },
        {
          model: User,
        },
      ],
    });
  }

  @Get('/:id', { summary: '按ID查询用户会员卡' })
  async show(@Param('id') id: number) {
    const res = await this.service.findById(id);
    if (!res) {
      throw new CustomError('未找到指定用户会员卡');
    }
    return res;
  }

  @Post('/', { summary: '新增用户会员卡' })
  async create(@Body() info: any) {
    // 使用 createCard 方法，确保购买时间有默认值
    const res = await this.service.createCard(info);
    return res;
  }

  @Put('/:id', { summary: '更新用户会员卡' })
  async update(@Param('id') id: number, @Body() body: any) {
    await this.service.update({ id }, body);
    return true;
  }

  @Del('/:id', { summary: '删除用户会员卡' })
  async destroy(@Param('id') id: number) {
    await this.service.delete({ id });
    return true;
  }

  @Get('/:cardTypeId/users', { summary: '根据权益卡类型ID获取用户列表' })
  async getUsersByCardType(
    @Param('cardTypeId') cardTypeId: number,
    @Query() query: any
  ) {
    let { offset, limit } = query;
    const { offset: o, limit: l, current, pageSize, ...queryInfo } = query;
    void o;
    void l;

    if (current && pageSize) {
      offset = (current - 1) * pageSize;
      limit = pageSize;
    }
    const res = await this.service.getUsersByCardType({
      query: { ...queryInfo, cardTypeId },
      offset,
      limit,
    });
    return res;
  }

  @Get('/:customerId/membership-cards', {
    summary: '根据用户ID获取权益卡类型列表',
  })
  async getCardsByUser(
    @Param('customerId') customerId: number,
    @Query() query: any
  ) {
    let { offset, limit } = query;
    const { offset: o, limit: l, current, pageSize, ...queryInfo } = query;
    void o;
    void l;

    if (current && pageSize) {
      offset = (current - 1) * pageSize;
      limit = pageSize;
    }
    const res = await this.service.getCardsByUser({
      ...queryInfo,
      offset,
      limit,
      customerId,
    });
    return res;
  }
}
